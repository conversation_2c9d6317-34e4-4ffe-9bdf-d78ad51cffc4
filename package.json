{"name": "white-lotus-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "bcrypt": "^6.0.0", "lucide-react": "^0.544.0", "next": "15.5.3", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sequelize": "^6.37.7", "zod": "^4.1.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^6.0.0", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "typescript": "^5"}}