import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';

export interface InvoiceAttributes {
    id: string; // id is an auto-generated UUID
    invoice_number: string;
    client_name: string;
    amount: number;
    status: string;
    due_date: Date; // last payment date
    invoice_file_name: string;
    invoice_file_url: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

type InvoiceCreationAttributes = Optional<InvoiceAttributes, 'id'>;

interface InvoiceInstance extends Model<InvoiceAttributes, InvoiceCreationAttributes>, InvoiceAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type InvoiceStatic = typeof Model & { associate: (models: any) => void } & (new (
    values?: Record<string, unknown>,
    options?: BuildOptions
) => InvoiceInstance);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createInvoiceModel = (sequelize: Sequelize, DataTypes: any) => {
    const invoices = sequelize.define<InvoiceInstance>(
        'invoices',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            invoice_number: {
                type: DataTypes.STRING,
                allowNull: false
            },
            client_name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            amount: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: false
            },
            status: {
                type: DataTypes.ENUM('PENDING', 'PAID', 'OVERDUE'),
                allowNull: false
            },
            due_date: {
                type: DataTypes.DATE,
                allowNull: false
            },
            invoice_file_name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            invoice_file_url: {
                type: DataTypes.STRING,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as InvoiceStatic;

    //
    // await invoices.sync({ alter: true })

    return invoices;
};

export default createInvoiceModel;
