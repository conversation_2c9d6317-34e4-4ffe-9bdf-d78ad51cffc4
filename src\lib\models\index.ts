// src/lib/models/index.ts
import { Sequelize, DataTypes } from 'sequelize';
import process from 'process';

// Import model creators
import createUserModel from './users';
import createInvoiceModel from './invoices';

const { DATABASE_NAME, DATABASE_USER, DATABASE_PASS, DATABASE_HOST, DATABASE_PORT } = process.env;

// Initialize Sequelize
export const sequelize = new Sequelize(
  DATABASE_NAME!,
  DATABASE_USER!,
  DATABASE_PASS!,
  {
    host: DATABASE_HOST!,
    dialect: 'postgres',
    port: parseInt(DATABASE_PORT || '5432', 10),
    pool: {
      max: 10,
      min: 2,
      acquire: 30000, // Max time in ms to get a connection before throwing error
      idle: 10000, // Connection closes after idle timeout
    },
    logging: false,
  }
);

// Define type for db object
interface DB {
  sequelize: Sequelize;
  Sequelize: typeof Sequelize;
  User?: ReturnType<typeof createUserModel>;
  Invoice?: ReturnType<typeof createInvoiceModel>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // Allow dynamic model properties for associations
}

const db: DB = {
  sequelize,
  Sequelize,
};

// Initialize models explicitly
const User = createUserModel(sequelize, DataTypes);
const Invoice = createInvoiceModel(sequelize, DataTypes);

// Add models to db object
db.User = User;
db.Invoice = Invoice;

// Setup associations if defined
Object.keys(db).forEach((modelName) => {
  const model = db[modelName];
  if ('associate' in model && typeof model.associate === 'function') {
    model.associate(db);
  }
});

export default db;
