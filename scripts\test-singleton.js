/**
 * Test script to verify the Sequelize singleton is working correctly
 * Run with: node scripts/test-singleton.js
 */

const { DatabaseManager } = require('../src/lib/database');
const { models, dbInstance } = require('../src/lib/models');

async function testSingleton() {
  console.log('🧪 Testing Sequelize Singleton...\n');

  try {
    // Test 1: Multiple initialization calls should only initialize once
    console.log('Test 1: Multiple initialization calls');
    console.log('- First initialization call...');
    const start1 = Date.now();
    await DatabaseManager.initialize();
    const time1 = Date.now() - start1;
    console.log(`  ✅ First call completed in ${time1}ms`);

    console.log('- Second initialization call...');
    const start2 = Date.now();
    await DatabaseManager.initialize();
    const time2 = Date.now() - start2;
    console.log(`  ✅ Second call completed in ${time2}ms (should be much faster)`);

    console.log('- Third initialization call...');
    const start3 = Date.now();
    await DatabaseManager.initialize();
    const time3 = Date.now() - start3;
    console.log(`  ✅ Third call completed in ${time3}ms (should be instant)\n`);

    // Test 2: Database ready status
    console.log('Test 2: Database ready status');
    console.log(`- Database ready: ${DatabaseManager.isReady()}`);
    console.log('  ✅ Database is ready\n');

    // Test 3: Models are available
    console.log('Test 3: Models availability');
    const modelNames = Object.keys(models).filter(key => key !== 'sequelize' && key !== 'Sequelize');
    console.log(`- Available models: ${modelNames.join(', ')}`);
    console.log('  ✅ Models loaded successfully\n');

    // Test 4: Database operations
    console.log('Test 4: Database operations');
    const userCount = await models.users.count();
    const invoiceCount = await models.invoices.count();
    console.log(`- User count: ${userCount}`);
    console.log(`- Invoice count: ${invoiceCount}`);
    console.log('  ✅ Database operations working\n');

    // Test 5: Admin user exists
    console.log('Test 5: Default admin user');
    const adminUser = await models.users.findOne({
      where: { email: '<EMAIL>' }
    });
    if (adminUser) {
      console.log(`- Admin user found: ${adminUser.first_name} ${adminUser.last_name}`);
      console.log('  ✅ Default admin user created successfully\n');
    } else {
      console.log('  ❌ Default admin user not found\n');
    }

    // Test 6: Singleton instance consistency
    console.log('Test 6: Singleton instance consistency');
    const instance1 = dbInstance;
    const instance2 = dbInstance;
    console.log(`- Same instance: ${instance1 === instance2}`);
    console.log('  ✅ Singleton pattern working correctly\n');

    console.log('🎉 All tests passed! Sequelize singleton is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Clean up
    try {
      await dbInstance.close();
      console.log('\n🔌 Database connection closed.');
    } catch (error) {
      console.error('Error closing database:', error.message);
    }
    process.exit(0);
  }
}

// Run the test
testSingleton();
